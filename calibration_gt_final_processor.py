#!/usr/bin/env python3
"""
calibration_gt最终处理器 - 修复格式问题

修复内容：
1. 去掉冗余的描述信息
2. 数字孪生ID格式：2壹、3柒、虚拟二（无下划线）
3. 保持RLCard格式中的下划线（用于程序处理）

位置：已移动到 tools/data_processing/calibration_gt/final_processor.py
"""

import os
import json
import shutil
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 导入项目核心模块 - 使用修复后的Phase2Integrator
from src.modules.phase2_integrator import Phase2Integrator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class FinalProcessorConfig:
    """最终处理器配置"""
    source_dir: str = "legacy_assets/ceshi/calibration_gt"
    output_dir: str = "output/calibration_gt_final_with_digital_twin"
    image_width: int = 640
    image_height: int = 320
    
    # 有效卡牌类别（21个：20个数值 + 1个暗牌）
    valid_card_labels: List[str] = None
    
    def __post_init__(self):
        if self.valid_card_labels is None:
            self.valid_card_labels = [
                "一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
                "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾",
                "暗"
            ]

class CalibrationGTFinalProcessor:
    """calibration_gt最终处理器"""
    
    def __init__(self, config: FinalProcessorConfig):
        self.config = config
        # 使用修复后的Phase2Integrator
        self.digital_twin_controller = Phase2Integrator()
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

        # 记录系统版本信息
        self.logger.info("使用修复后的Phase2Integrator")
        self.logger.info("当前策略：第二阶段完整功能（已修复暗牌处理）")
        self.logger.info("包含模块：数据验证器、继承器、区域流转器、暗牌处理器、ID分配器、遮挡补偿器")
        
        # 处理统计
        self.stats = {
            "total_frames": 0,
            "processed_frames": 0,
            "frames_with_cards": 0,
            "frames_without_cards": 0,
            "total_cards": 0,
            "successful_assignments": 0,
            "failed_frames": 0,  # 修改为数字
            "failed_frame_list": [],  # 失败帧列表
            "error_details": {}
        }
        
    def process_final_dataset(self) -> Dict[str, Any]:
        """最终处理calibration_gt数据集"""
        self.logger.info("🎯 开始最终处理calibration_gt数据集...")
        
        # 创建输出目录结构
        self._create_output_structure()
        
        # 获取所有标注文件
        all_frame_files = self._get_all_frames()
        self.stats["total_frames"] = len(all_frame_files)
        
        self.logger.info(f"📊 找到 {len(all_frame_files)} 个标注文件")
        
        # 按帧序列处理所有文件
        for frame_file in sorted(all_frame_files):
            try:
                self._process_single_frame_final(frame_file)
                self.stats["processed_frames"] += 1
                
                if self.stats["processed_frames"] % 50 == 0:
                    self.logger.info(f"📈 已处理 {self.stats['processed_frames']}/{self.stats['total_frames']} 帧")
                    
            except Exception as e:
                frame_name = frame_file.stem
                self.stats["failed_frames"] += 1
                self.stats["failed_frame_list"].append(frame_name)
                self.stats["error_details"][frame_name] = str(e)
                self.logger.error(f"❌ 处理帧 {frame_name} 失败: {e}")
                
                # 即使失败也要保存原始文件
                self._save_failed_frame(frame_file, str(e))
                continue
                
        # 生成最终报告
        final_report = self._generate_final_report()
        
        self.logger.info("🎉 calibration_gt最终处理完成")
        return final_report
    
    def _create_output_structure(self):
        """创建输出目录结构"""
        output_path = Path(self.config.output_dir)
        
        # 创建主要目录
        directories = [
            output_path,
            output_path / "images",
            output_path / "labels", 
            output_path / "rlcard_format",
            output_path / "reports",
            output_path / "failed_frames"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            
        self.logger.info(f"📁 输出目录结构创建完成: {output_path}")
    
    def _get_all_frames(self) -> List[Path]:
        """获取所有标注文件"""
        labels_dir = Path(self.config.source_dir) / "labels"
        if not labels_dir.exists():
            raise FileNotFoundError(f"标注目录不存在: {labels_dir}")
        
        # 获取所有JSON文件
        label_files = list(labels_dir.glob("*.json"))
        self.logger.info(f"📊 找到 {len(label_files)} 个标注文件")
        return label_files
    
    def _process_single_frame_final(self, frame_file: Path):
        """最终处理单个帧文件"""
        frame_name = frame_file.stem
        self.logger.debug(f"🔄 处理帧: {frame_name}")

        # 读取原始标注
        with open(frame_file, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        # 分析原始标注中的卡牌
        card_shapes, non_card_shapes = self._analyze_original_shapes(original_data)
        
        if card_shapes:
            # 有卡牌的帧：转换为CardDetection并使用数字孪生系统
            self.stats["frames_with_cards"] += 1
            self.stats["total_cards"] += len(card_shapes)
            
            # 转换为数字孪生系统V3.0所需格式
            card_detections = self._convert_shapes_to_detections(card_shapes)

            # 🔧 修复：为边界检测器添加所有原始标签（包括UI标签）
            all_detections = self._convert_all_shapes_to_detections(original_data.get("shapes", []))

            # 使用数字孪生主控器处理（传递所有检测数据用于边界检测）
            dt_result = self.digital_twin_controller.process_frame(all_detections)

            if not dt_result.success:
                self.logger.error(f"数字孪生处理失败: {dt_result.validation_errors}")
                self.stats["failed_frames"] += 1

                # 即使处理失败，也要保存原始数据，避免丢失帧
                self.logger.warning(f"保存失败帧的原始数据: {frame_name}")
                self._save_failed_frame_with_original_data(frame_file, original_data, str(dt_result.validation_errors))
                return

            # 转换为兼容格式
            compatible_result = {
                "digital_twin_cards": dt_result.processed_cards,
                "statistics": dt_result.statistics,
                "success": dt_result.success,
                "strategy_used": "Phase2Integrator"  # 添加兼容性字段
            }

            # 生成双轨输出（修复格式）
            dual_result = self._generate_dual_format_final(
                compatible_result, original_data, card_shapes, non_card_shapes
            )

            # 更新统计
            self.stats["successful_assignments"] += len(dt_result.processed_cards)

            # 记录主控器的详细统计
            if dt_result.statistics:
                summary = dt_result.statistics.get('summary', {})

                # 检查是否启动了数字孪生
                digital_twin_enabled = dt_result.statistics.get('digital_twin_enabled', True)
                if digital_twin_enabled:
                    strategy_used = getattr(dt_result, 'strategy_used', 'Phase2Integrator')
                    processing_time = getattr(dt_result, 'processing_time', 0.0)
                    self.logger.info(f"✅ 数字孪生已启动 - 策略: {strategy_used}, "
                                   f"处理时间: {processing_time:.3f}s")
                    self.logger.info(f"详细统计 - 继承率: {summary.get('inheritance_rate', 0):.1%}, "
                                   f"流转率: {summary.get('transition_rate', 0):.1%}, "
                                   f"暗牌成功率: {summary.get('dark_card_success_rate', 0):.1%}, "
                                   f"补偿率: {summary.get('compensation_rate', 0):.1%}")
                else:
                    # 数字孪生未启动，记录原因
                    activation_info = dt_result.statistics.get('activation_decision', {})
                    self.logger.info(f"⚠️ 数字孪生未启动 - 原因: {activation_info.get('reason', '未知')}")
                    self.logger.info(f"卡牌状态 - 数量: {activation_info.get('card_count', 0)}, "
                                   f"合格率: {activation_info.get('qualified_ratio', 0):.1%}")
                    self.logger.info("原始数据已保留，等待牌局完全展开")
            
        else:
            # 无卡牌的帧：完全保留原始内容
            self.stats["frames_without_cards"] += 1
            
            # 创建保留原始内容的结果
            dual_result = {
                'rlcard_format': {"hand": [], "public": [], "opponents": []},
                'anylabeling_format': original_data.copy()
            }
        
        # 保存结果（最终版本）
        self._save_frame_results_final(frame_name, original_data, dual_result)
    
    def _analyze_original_shapes(self, original_data: Dict) -> Tuple[List[Dict], List[Dict]]:
        """分析原始shapes，分离卡牌和非卡牌"""
        card_shapes = []
        non_card_shapes = []

        # 🔧 添加区域4数据调试
        region_4_shapes = [shape for shape in original_data.get("shapes", []) if shape.get("group_id") == 4]
        if region_4_shapes:
            self.logger.info(f"🔧 [调试] _analyze_original_shapes发现区域4数据: {len(region_4_shapes)}张")
            for shape in region_4_shapes:
                label = shape.get("label", "")
                group_id = shape.get("group_id")
                self.logger.info(f"🔧 [调试] 区域4 shape: 标签='{label}', group_id={group_id}")
                self.logger.info(f"🔧 [调试] 标签'{label}'在valid_card_labels中: {label in self.config.valid_card_labels}")
        else:
            self.logger.info(f"🔧 [调试] _analyze_original_shapes未发现区域4数据")

        for shape in original_data.get("shapes", []):
            label = shape.get("label", "")
            if label in self.config.valid_card_labels:
                card_shapes.append(shape)
                # 🔧 调试区域4卡牌分类
                if shape.get("group_id") == 4:
                    self.logger.info(f"🔧 [调试] 区域4卡牌被分类为card_shapes: 标签='{label}'")
            else:
                non_card_shapes.append(shape)
                # 🔧 调试区域4非卡牌分类
                if shape.get("group_id") == 4:
                    self.logger.info(f"🔧 [调试] 区域4被分类为non_card_shapes: 标签='{label}'")

        self.logger.debug(f"分析完成: {len(card_shapes)} 个卡牌, {len(non_card_shapes)} 个非卡牌")
        return card_shapes, non_card_shapes
    
    def _convert_shapes_to_detections(self, card_shapes: List[Dict]) -> List[Dict]:
        """将原始shapes转换为数字孪生系统V3.0所需的检测格式"""
        detections = []

        for shape in card_shapes:
            # 提取原始边界框信息
            points = shape.get("points", [])
            if len(points) != 4:
                self.logger.warning(f"无效的边界框点数: {len(points)}")
                continue

            # 计算边界框 [x1, y1, x2, y2] - 数字孪生系统V3.0使用的格式
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]

            x_min, x_max = min(x_coords), max(x_coords)
            y_min, y_max = min(y_coords), max(y_coords)

            bbox = [x_min, y_min, x_max, y_max]  # [x1, y1, x2, y2]格式

            # 创建检测字典（符合数字孪生系统V3.0接口）
            # 安全地转换数据类型
            try:
                confidence = float(shape.get("score", 1.0))
            except (ValueError, TypeError):
                confidence = 1.0

            try:
                group_id = int(shape.get("group_id", 0))
            except (ValueError, TypeError):
                group_id = 0

            detection = {
                "label": shape.get("label", ""),
                "bbox": bbox,
                "confidence": confidence,
                "group_id": group_id,
                # 保留原始信息用于后续处理
                "region_name": shape.get("region_name", ""),
                "owner": shape.get("owner", ""),
                "original_points": points,
                "original_shape": shape
            }

            detections.append(detection)

        self.logger.debug(f"转换完成: {len(detections)} 个检测对象")
        return detections

    def _convert_all_shapes_to_detections(self, all_shapes: List[Dict]) -> List[Dict]:
        """将所有shapes（包括UI标签）转换为检测格式，用于边界检测"""
        detections = []

        for shape in all_shapes:
            # 提取基本信息
            label = shape.get("label", "")

            # 对于UI标签（如"你赢了"），可能没有完整的边界框信息
            points = shape.get("points", [])
            if len(points) == 4:
                # 有完整边界框的shape
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
            else:
                # UI标签可能没有边界框，使用默认值
                bbox = [0, 0, 0, 0]

            # 安全地转换数据类型
            try:
                confidence = float(shape.get("score", 1.0))
            except (ValueError, TypeError):
                confidence = 1.0

            try:
                group_id = int(shape.get("group_id", 0))
            except (ValueError, TypeError):
                group_id = 0

            detection = {
                "label": label,
                "bbox": bbox,
                "confidence": confidence,
                "group_id": group_id,
                "region_name": shape.get("region_name", ""),
                "owner": shape.get("owner", ""),
                "original_points": points,
                "original_shape": shape
            }

            detections.append(detection)

        self.logger.debug(f"转换所有shapes完成: {len(detections)} 个检测对象（包括UI标签）")
        return detections

    def _generate_dual_format_final(self, dt_result: Dict, original_data: Dict,
                                   card_shapes: List[Dict], non_card_shapes: List[Dict]) -> Dict:
        """生成双轨输出格式，修复格式问题"""

        # 1. 生成RLCard格式（保留下划线用于程序处理）
        rlcard_format = self._generate_rlcard_with_underscores(dt_result["digital_twin_cards"])

        # 2. 生成修复的AnyLabeling格式（无下划线，无冗余描述）
        enhanced_shapes = []

        # 处理卡牌shapes（修复格式）
        twin_cards_by_label_pos = self._create_twin_card_mapping(dt_result["digital_twin_cards"])

        self.logger.info(f"🔄 开始处理{len(card_shapes)}张卡牌shapes，匹配数字孪生ID")

        matched_count = 0
        unmatched_count = 0

        for i, shape in enumerate(card_shapes):
            enhanced_shape = shape.copy()  # 完全复制原始shape
            original_label = shape.get("label", "")

            self.logger.debug(f"🔄 处理shape {i+1}/{len(card_shapes)}: 原始label='{original_label}'")

            # 根据位置和标签找到对应的数字孪生卡牌
            twin_card = self._find_matching_twin_card(shape, twin_cards_by_label_pos)

            if twin_card:
                matched_count += 1

                # 修复数字孪生ID格式：去掉下划线
                twin_id = twin_card.get('twin_id', '') if isinstance(twin_card, dict) else getattr(twin_card, 'twin_id', '')
                clean_twin_id = self._remove_underscore_from_twin_id(twin_id)

                # 更新label为修复后的数字孪生ID格式
                enhanced_shape["label"] = clean_twin_id

                # 清空description（去掉冗余信息）
                enhanced_shape["description"] = ""

                # 添加简化的数字孪生属性
                if "attributes" not in enhanced_shape:
                    enhanced_shape["attributes"] = {}

                is_virtual = twin_card.get('is_virtual', False) if isinstance(twin_card, dict) else getattr(twin_card, 'is_virtual', False)
                enhanced_shape["attributes"].update({
                    "digital_twin_id": clean_twin_id,
                    "is_virtual": is_virtual,
                    "processed_timestamp": datetime.now().isoformat(),
                    "processing_version": "modular_v2.0"
                })

                self.logger.info(f"✅ 匹配成功 {i+1}: '{original_label}' → '{clean_twin_id}'")
            else:
                unmatched_count += 1
                # 如果没有找到匹配的数字孪生卡牌，保留原始信息
                enhanced_shape["description"] = ""
                self.logger.warning(f"❌ 未匹配 {i+1}: '{original_label}' 保持原样")

            enhanced_shapes.append(enhanced_shape)

        self.logger.info(f"🔄 卡牌处理完成: 匹配成功{matched_count}张, 未匹配{unmatched_count}张")

        # 添加非卡牌shapes（完全保留原始）
        for shape in non_card_shapes:
            enhanced_shapes.append(shape.copy())

        # 构建修复的AnyLabeling格式
        anylabeling_format = original_data.copy()
        anylabeling_format["shapes"] = enhanced_shapes

        # 添加简化的处理元数据
        anylabeling_format["processing_metadata"] = {
            "digital_twin_version": "final_v1.0",
            "processing_timestamp": datetime.now().isoformat(),
            "source_dataset": "calibration_gt",
            "format_fixes": ["removed_underscores", "cleaned_descriptions"],
            "coordinates_preserved": True
        }

        return {
            'rlcard_format': rlcard_format,
            'anylabeling_format': anylabeling_format
        }

    def _remove_underscore_from_twin_id(self, twin_id: str) -> str:
        """去掉数字孪生ID中的下划线，特殊处理暗牌"""
        # 处理格式：2_壹 -> 2壹, 虚拟_二 -> 虚拟二
        # 特殊处理暗牌：1_暗 -> 1暗 (保持原样，因为暗牌修复应该在数字孪生系统中完成)
        if "_" in twin_id:
            return twin_id.replace("_", "")
        return twin_id

    def _generate_rlcard_with_underscores(self, digital_twin_cards: List) -> Dict:
        """生成包含下划线的RLCard格式（用于程序处理）"""
        rlcard_data = {"hand": [], "public": [], "opponents": []}

        for card in digital_twin_cards:
            # 获取卡牌属性（兼容字典和对象格式）
            card_label = card.get('label', '') if isinstance(card, dict) else getattr(card, 'label', '')
            card_twin_id = card.get('twin_id', '') if isinstance(card, dict) else getattr(card, 'twin_id', '')
            card_confidence = card.get('confidence', 1.0) if isinstance(card, dict) else getattr(card, 'confidence', 1.0)
            card_region_name = card.get('region_name', '') if isinstance(card, dict) else getattr(card, 'region_name', '')

            # 转换卡牌值和花色
            card_value = self._convert_label_to_value(card_label)
            suit = 1 if card_label in ["壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾"] else 0

            # 创建RLCard格式条目：[值, 花色, 数字孪生ID(带下划线), 置信度]
            rlcard_entry = [
                card_value,
                suit,
                card_twin_id,  # 保留下划线用于程序处理
                card_confidence
            ]

            # 根据区域分配到不同类别
            if "手牌" in card_region_name:
                rlcard_data["hand"].append(rlcard_entry)
            elif "公共" in card_region_name:
                rlcard_data["public"].append(rlcard_entry)
            else:
                rlcard_data["opponents"].append(rlcard_entry)

        return rlcard_data

    def _convert_label_to_value(self, label: str) -> int:
        """转换卡牌标签为数值"""
        label_to_value = {
            "一": 1, "二": 2, "三": 3, "四": 4, "五": 5,
            "六": 6, "七": 7, "八": 8, "九": 9, "十": 10,
            "壹": 1, "贰": 2, "叁": 3, "肆": 4, "伍": 5,
            "陆": 6, "柒": 7, "捌": 8, "玖": 9, "拾": 10,
            "暗": 0
        }
        return label_to_value.get(label, 0)

    def _create_twin_card_mapping(self, digital_twin_cards: List) -> Dict:
        """创建数字孪生卡牌的映射表"""
        mapping = {}

        self.logger.info(f"🗺️ 创建数字孪生卡牌映射: {len(digital_twin_cards)} 张卡牌")

        for i, card in enumerate(digital_twin_cards):
            # 使用标签和位置作为键
            card_label = card.get('label', '') if isinstance(card, dict) else getattr(card, 'label', '')
            card_bbox = card.get('bbox', []) if isinstance(card, dict) else getattr(card, 'bbox', [])
            twin_id = card.get('twin_id', '') if isinstance(card, dict) else getattr(card, 'twin_id', '')

            self.logger.debug(f"🗺️ 卡牌{i+1}: label='{card_label}', twin_id='{twin_id}', bbox={card_bbox}")

            if card_label and len(card_bbox) >= 2:
                key = f"{card_label}_{card_bbox[0]:.1f}_{card_bbox[1]:.1f}"
                mapping[key] = card
                self.logger.debug(f"🗺️ 添加映射: {key} → {card_label}")
            else:
                self.logger.warning(f"⚠️ 跳过无效卡牌: label='{card_label}', bbox长度={len(card_bbox) if card_bbox else 0}")

        self.logger.info(f"🗺️ 映射创建完成: {len(mapping)} 个有效映射")
        return mapping

    def _find_matching_twin_card(self, shape: Dict, twin_cards_mapping: Dict):
        """根据原始shape找到匹配的数字孪生卡牌"""
        label = shape.get("label", "")
        points = shape.get("points", [])

        if not points or len(points) != 4:
            self.logger.debug(f"🔍 跳过无效shape: label={label}, points数量={len(points) if points else 0}")
            return None

        # 计算位置
        x_coords = [p[0] for p in points]
        y_coords = [p[1] for p in points]
        x_min, y_min = min(x_coords), min(y_coords)

        self.logger.debug(f"🔍 查找匹配: 原始label='{label}', 位置=({x_min:.1f}, {y_min:.1f})")
        self.logger.debug(f"🔍 可用的数字孪生卡牌映射: {list(twin_cards_mapping.keys())}")

        # 🔧 修复：不再使用原始label进行精确匹配，因为数字孪生处理后label已改变
        # 直接进行位置匹配
        best_match = None
        min_distance = float('inf')

        for mapping_key, twin_card in twin_cards_mapping.items():
            card_label = twin_card.get('label', '') if isinstance(twin_card, dict) else getattr(twin_card, 'label', '')
            card_bbox = twin_card.get('bbox', []) if isinstance(twin_card, dict) else getattr(twin_card, 'bbox', [])

            if len(card_bbox) >= 2:
                # 计算位置距离
                distance = abs(card_bbox[0] - x_min) + abs(card_bbox[1] - y_min)

                # 🔧 修复：检查原始标签是否匹配数字孪生卡牌的基础类型
                is_label_compatible = self._is_label_compatible(label, card_label)

                self.logger.debug(f"🔍 检查匹配: twin_label='{card_label}', twin_pos=({card_bbox[0]:.1f}, {card_bbox[1]:.1f}), "
                                f"距离={distance:.1f}, 标签兼容={is_label_compatible}")

                if is_label_compatible and distance < 10 and distance < min_distance:
                    best_match = twin_card
                    min_distance = distance
                    self.logger.debug(f"🎯 找到更好匹配: {card_label}, 距离={distance:.1f}")

        if best_match:
            twin_label = best_match.get('label', '') if isinstance(best_match, dict) else getattr(best_match, 'label', '')
            self.logger.info(f"✅ 匹配成功: '{label}' → '{twin_label}', 距离={min_distance:.1f}")
        else:
            self.logger.warning(f"❌ 未找到匹配: 原始label='{label}', 位置=({x_min:.1f}, {y_min:.1f})")

        return best_match

    def _is_label_compatible(self, original_label: str, twin_label: str) -> bool:
        """检查原始标签和数字孪生标签是否兼容"""
        # 如果原始标签是"暗"，数字孪生标签应该包含"暗"
        if original_label == "暗":
            return "暗" in twin_label

        # 如果原始标签是具体牌面，数字孪生标签应该包含该牌面
        # 例如：原始="拾"，数字孪生="3拾" 或 "1拾暗"
        if original_label in twin_label:
            return True

        return False

    def _save_frame_results_final(self, frame_name: str, original_data: Dict, dual_result: Dict):
        """保存帧处理结果（最终版本）"""
        output_path = Path(self.config.output_dir)

        # 复制原始图片
        source_image = Path(self.config.source_dir) / "images" / f"{frame_name}.jpg"
        target_image = output_path / "images" / f"{frame_name}.jpg"

        if source_image.exists():
            shutil.copy2(source_image, target_image)
        else:
            self.logger.warning(f"⚠️ 源图片不存在: {source_image}")

        # 保存修复的AnyLabeling格式（无下划线，无冗余描述）
        labels_file = output_path / "labels" / f"{frame_name}.json"
        with open(labels_file, 'w', encoding='utf-8') as f:
            json.dump(dual_result['anylabeling_format'], f, ensure_ascii=False, indent=2)

        # 保存RLCard格式（保留下划线）
        rlcard_file = output_path / "rlcard_format" / f"{frame_name}.json"
        with open(rlcard_file, 'w', encoding='utf-8') as f:
            json.dump(dual_result['rlcard_format'], f, ensure_ascii=False, indent=2)

    def _save_failed_frame(self, frame_file: Path, error_message: str):
        """保存失败帧的原始内容和错误信息"""
        frame_name = frame_file.stem
        output_path = Path(self.config.output_dir)

        # 复制原始文件到失败目录
        failed_dir = output_path / "failed_frames"

        # 复制原始标注文件
        failed_label_file = failed_dir / f"{frame_name}.json"
        shutil.copy2(frame_file, failed_label_file)

        # 复制原始图片（如果存在）
        source_image = Path(self.config.source_dir) / "images" / f"{frame_name}.jpg"
        if source_image.exists():
            failed_image_file = failed_dir / f"{frame_name}.jpg"
            shutil.copy2(source_image, failed_image_file)

        # 保存错误信息
        error_file = failed_dir / f"{frame_name}_error.txt"
        with open(error_file, 'w', encoding='utf-8') as f:
            f.write(f"帧: {frame_name}\n")
            f.write(f"错误时间: {datetime.now().isoformat()}\n")
            f.write(f"错误信息: {error_message}\n")

        self.logger.info(f"💾 失败帧 {frame_name} 已保存到失败目录")

    def _save_failed_frame_with_original_data(self, frame_file: Path, original_data: Dict, error_message: str):
        """保存失败帧的原始数据，确保不丢失帧"""
        frame_name = frame_file.stem

        # 保存到失败帧目录
        self._save_failed_frame(frame_file, error_message)

        # 同时保存到主输出目录，使用原始数据
        try:
            # 复制原始图片
            source_image = Path(self.config.source_dir) / "images" / f"{frame_name}.jpg"
            if source_image.exists():
                output_image = Path(self.config.output_dir) / "images" / f"{frame_name}.jpg"
                shutil.copy2(source_image, output_image)

            # 保存原始标注（添加错误标记）
            output_label = Path(self.config.output_dir) / "labels" / f"{frame_name}.json"
            failed_data = original_data.copy()
            failed_data["processing_error"] = error_message
            failed_data["processing_status"] = "failed"

            with open(output_label, 'w', encoding='utf-8') as f:
                json.dump(failed_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"已保存失败帧的原始数据: {frame_name}")

        except Exception as e:
            self.logger.error(f"保存失败帧原始数据时出错: {e}")

    def _generate_final_report(self) -> Dict[str, Any]:
        """生成最终的处理报告"""
        success_rate = (
            self.stats["successful_assignments"] / self.stats["total_cards"]
            if self.stats["total_cards"] > 0 else 0.0
        )

        processing_rate = (
            self.stats["processed_frames"] / self.stats["total_frames"]
            if self.stats["total_frames"] > 0 else 0.0
        )

        final_report = {
            "processing_summary": {
                "total_frames_found": self.stats["total_frames"],
                "processed_frames": self.stats["processed_frames"],
                "failed_frames": self.stats["failed_frames"],
                "processing_success_rate": processing_rate,
                "frames_with_cards": self.stats["frames_with_cards"],
                "frames_without_cards": self.stats["frames_without_cards"]
            },
            "card_processing": {
                "total_cards_detected": self.stats["total_cards"],
                "successful_id_assignments": self.stats["successful_assignments"],
                "id_assignment_success_rate": success_rate
            },
            "format_improvements": {
                "removed_underscores_in_anylabeling": True,
                "cleaned_descriptions": True,
                "preserved_underscores_in_rlcard": True,
                "coordinates_preserved": True,
                "processing_mode": "final_clean_format"
            },
            "failed_frames_analysis": {
                "failed_frame_names": self.stats["failed_frame_list"],
                "error_details": self.stats["error_details"],
                "failed_frames_saved": len(self.stats["failed_frame_list"])
            },
            "output_structure": {
                "output_directory": self.config.output_dir,
                "images_generated": self.stats["processed_frames"],
                "labels_generated": self.stats["processed_frames"],
                "rlcard_files_generated": self.stats["processed_frames"],
                "failed_frames_preserved": len(self.stats["failed_frame_list"])
            },
            "timestamp": datetime.now().isoformat(),
            "configuration": {
                "source_directory": self.config.source_dir,
                "image_dimensions": f"{self.config.image_width}x{self.config.image_height}",
                "valid_card_labels_count": len(self.config.valid_card_labels),
                "processing_version": "modular_v2.0",
                "digital_twin_system": "第二阶段模块化系统",
                "modules_used": [
                    "数据验证器", "简单继承器", "区域流转器",
                    "暗牌处理器", "基础ID分配器", "遮挡补偿器"
                ]
            },
            "digital_twin_system_info": {
                "system_version": "第二阶段模块化系统",
                "key_improvements": [
                    "区域流转逻辑修复：1二1 → 1二2",
                    "暗牌关联功能：1暗 → 1二暗",
                    "智能遮挡补偿：避免过度补偿",
                    "模块化架构：独立开发和测试"
                ],
                "system_status": self.digital_twin_controller.get_system_status() if hasattr(self.digital_twin_controller, 'get_system_status') else {}
            }
        }

        # 保存最终报告
        report_file = Path(self.config.output_dir) / "reports" / "final_processing_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, ensure_ascii=False, indent=2)

        return final_report

def main():
    """主函数 - 最终处理"""
    print("🎯 calibration_gt最终处理器")
    print("=" * 60)
    print("📋 最终修复:")
    print("   ✅ 去掉冗余的描述信息")
    print("   ✅ 数字孪生ID格式：2壹、3柒、虚拟二（无下划线）")
    print("   ✅ RLCard格式保留下划线（程序处理需要）")
    print("   ✅ 完全保留原始坐标和区域信息")
    print("=" * 60)

    # 创建配置
    config = FinalProcessorConfig()

    # 检查源目录
    if not Path(config.source_dir).exists():
        print(f"❌ 源目录不存在: {config.source_dir}")
        return

    print(f"📂 源目录: {config.source_dir}")
    print(f"📁 输出目录: {config.output_dir}")
    print(f"🎴 有效卡牌类别: {len(config.valid_card_labels)} 个")
    print()

    # 创建处理器并执行
    processor = CalibrationGTFinalProcessor(config)

    try:
        print("🚀 开始最终处理...")
        final_report = processor.process_final_dataset()

        print("\n🎉 最终处理完成!")
        print(f"📊 处理统计:")
        print(f"   - 找到帧数: {final_report['processing_summary']['total_frames_found']}")
        print(f"   - 成功处理: {final_report['processing_summary']['processed_frames']}")
        print(f"   - 失败帧数: {final_report['processing_summary']['failed_frames']}")
        print(f"   - 处理成功率: {final_report['processing_summary']['processing_success_rate']:.2%}")
        print(f"   - 有卡牌帧: {final_report['processing_summary']['frames_with_cards']}")
        print(f"   - 无卡牌帧: {final_report['processing_summary']['frames_without_cards']}")
        print(f"   - 总卡牌数: {final_report['card_processing']['total_cards_detected']}")
        print(f"   - ID分配成功率: {final_report['card_processing']['id_assignment_success_rate']:.2%}")

        print(f"\n📁 输出文件:")
        print(f"   - 图片: {final_report['output_structure']['images_generated']} 个")
        print(f"   - 标注文件: {final_report['output_structure']['labels_generated']} 个（格式已修复）")
        print(f"   - RLCard格式: {final_report['output_structure']['rlcard_files_generated']} 个")
        print(f"   - 失败帧保存: {final_report['output_structure']['failed_frames_preserved']} 个")

        print(f"\n✨ 格式修复:")
        print(f"   - ✅ AnyLabeling格式：去掉下划线和冗余描述")
        print(f"   - ✅ RLCard格式：保留下划线用于程序处理")
        print(f"   - ✅ 坐标信息：100%保留原始人工标注")

        if final_report['processing_summary']['failed_frames'] > 0:
            print(f"\n⚠️ 失败帧详情:")
            for frame_name, error in final_report['failed_frames_analysis']['error_details'].items():
                print(f"   - {frame_name}: {error}")

        print(f"\n🎉 所有文件已保存到: {config.output_dir}")

    except Exception as e:
        print(f"❌ 处理失败: {e}")
        logger.exception("处理过程中发生错误")

if __name__ == "__main__":
    main()
