#!/usr/bin/env python3
"""
数字孪生ID功能区域6继承问题调试脚本
专门分析frame_00274.jpg的卡牌流转路径问题

问题描述：
- 测试素材文档：frame_00274.jpg区域6应该从下到上为：2九 1八 1十
- 实际输出：1八 1十 1九
- 关键问题：2九没有正确继承，变成了1九

对比案例：
- frame_00346.jpg和frame_00362.jpg都能正常继承
"""

import json
import os
from typing import Dict, List, Any, Optional
from pathlib import Path

class Frame274InheritanceDebugger:
    def __init__(self):
        self.output_dir = Path("output/calibration_gt_final_with_digital_twin/labels")
        self.debug_results = {}
        
    def load_frame_data(self, frame_name: str) -> Optional[Dict]:
        """加载指定帧的JSON数据"""
        file_path = self.output_dir / f"{frame_name}.json"
        if not file_path.exists():
            print(f"❌ 文件不存在: {file_path}")
            return None
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 读取文件失败 {file_path}: {e}")
            return None
    
    def extract_region_cards(self, frame_data: Dict, region_id: int) -> List[Dict]:
        """提取指定区域的卡牌数据"""
        if not frame_data or 'shapes' not in frame_data:
            return []
            
        region_cards = []
        for shape in frame_data['shapes']:
            if shape.get('group_id') == region_id:
                card_info = {
                    'label': shape.get('label', ''),
                    'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                    'position': self._get_center_position(shape.get('points', [])),
                    'region_id': region_id,
                    'shape': shape
                }
                region_cards.append(card_info)
        
        # 按Y坐标排序（从下到上）
        region_cards.sort(key=lambda x: -x['position'][1] if x['position'] else 0)
        return region_cards
    
    def _get_center_position(self, points: List) -> tuple:
        """计算卡牌中心位置"""
        if not points or len(points) < 4:
            return (0, 0)
        
        x_coords = [p[0] for p in points]
        y_coords = [p[1] for p in points]
        center_x = sum(x_coords) / len(x_coords)
        center_y = sum(y_coords) / len(y_coords)
        return (center_x, center_y)
    
    def analyze_frame_sequence(self, frame_names: List[str]) -> Dict:
        """分析帧序列的继承情况"""
        print(f"\n🔍 分析帧序列: {' → '.join(frame_names)}")
        
        results = {
            'frames': {},
            'inheritance_analysis': {},
            'issues': []
        }
        
        # 加载所有帧数据
        for frame_name in frame_names:
            frame_data = self.load_frame_data(frame_name)
            if frame_data:
                results['frames'][frame_name] = {
                    'region_6': self.extract_region_cards(frame_data, 6),
                    'region_3': self.extract_region_cards(frame_data, 3),
                    'region_1': self.extract_region_cards(frame_data, 1),
                    'all_regions': self._get_all_regions_summary(frame_data)
                }
        
        # 分析继承关系
        for i in range(1, len(frame_names)):
            prev_frame = frame_names[i-1]
            curr_frame = frame_names[i]
            
            if prev_frame in results['frames'] and curr_frame in results['frames']:
                inheritance = self._analyze_inheritance(
                    results['frames'][prev_frame],
                    results['frames'][curr_frame],
                    prev_frame,
                    curr_frame
                )
                results['inheritance_analysis'][f"{prev_frame}→{curr_frame}"] = inheritance
        
        return results
    
    def _get_all_regions_summary(self, frame_data: Dict) -> Dict:
        """获取所有区域的卡牌摘要"""
        summary = {}
        if not frame_data or 'shapes' not in frame_data:
            return summary
            
        for shape in frame_data['shapes']:
            region_id = shape.get('group_id', 0)
            label = shape.get('label', '')
            twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
            
            if region_id not in summary:
                summary[region_id] = []
            summary[region_id].append({
                'label': label,
                'twin_id': twin_id
            })
        
        return summary
    
    def _analyze_inheritance(self, prev_frame: Dict, curr_frame: Dict, 
                           prev_name: str, curr_name: str) -> Dict:
        """分析两帧之间的继承关系"""
        analysis = {
            'region_6_changes': {},
            'cross_region_flows': [],
            'missing_cards': [],
            'new_cards': []
        }
        
        # 分析区域6的变化
        prev_r6 = prev_frame['region_6']
        curr_r6 = curr_frame['region_6']
        
        print(f"\n📊 {prev_name} → {curr_name} 区域6变化分析:")
        prev_r6_str = [f"{c['label']}({c['digital_twin_id']})" for c in prev_r6]
        curr_r6_str = [f"{c['label']}({c['digital_twin_id']})" for c in curr_r6]
        print(f"  前一帧区域6: {prev_r6_str}")
        print(f"  当前帧区域6: {curr_r6_str}")
        
        # 查找继承的卡牌
        inherited_ids = set()
        for curr_card in curr_r6:
            curr_id = curr_card['digital_twin_id']
            for prev_card in prev_r6:
                if prev_card['digital_twin_id'] == curr_id:
                    inherited_ids.add(curr_id)
                    print(f"  ✅ 继承成功: {curr_card['label']}({curr_id})")
                    break
        
        # 查找消失的卡牌
        for prev_card in prev_r6:
            prev_id = prev_card['digital_twin_id']
            if prev_id not in inherited_ids:
                print(f"  ❌ 卡牌消失: {prev_card['label']}({prev_id})")
                analysis['missing_cards'].append(prev_card)
                
                # 查找这张卡牌是否流转到其他区域
                self._trace_card_flow(prev_card, curr_frame['all_regions'], analysis)
        
        # 查找新出现的卡牌
        for curr_card in curr_r6:
            curr_id = curr_card['digital_twin_id']
            found_in_prev = False
            for prev_card in prev_r6:
                if prev_card['digital_twin_id'] == curr_id:
                    found_in_prev = True
                    break
            
            if not found_in_prev:
                print(f"  🆕 新卡牌: {curr_card['label']}({curr_id})")
                analysis['new_cards'].append(curr_card)
                
                # 查找这张卡牌的来源
                self._trace_card_source(curr_card, prev_frame['all_regions'], analysis)
        
        return analysis
    
    def _trace_card_flow(self, missing_card: Dict, curr_all_regions: Dict, analysis: Dict):
        """追踪消失卡牌的流向"""
        card_id = missing_card['digital_twin_id']
        card_label = missing_card['label']
        
        for region_id, cards in curr_all_regions.items():
            if region_id == 6:  # 跳过原区域
                continue
                
            for card in cards:
                if card['twin_id'] == card_id:
                    flow_info = {
                        'card': missing_card,
                        'from_region': 6,
                        'to_region': region_id,
                        'description': f"{card_label}({card_id}) 从区域6流转到区域{region_id}"
                    }
                    analysis['cross_region_flows'].append(flow_info)
                    print(f"    🔄 找到流转: {flow_info['description']}")
                    return
        
        print(f"    ❓ 未找到流转目标: {card_label}({card_id})")
    
    def _trace_card_source(self, new_card: Dict, prev_all_regions: Dict, analysis: Dict):
        """追踪新卡牌的来源"""
        card_id = new_card['digital_twin_id']
        card_label = new_card['label']
        
        for region_id, cards in prev_all_regions.items():
            if region_id == 6:  # 跳过目标区域
                continue
                
            for card in cards:
                if card['twin_id'] == card_id:
                    flow_info = {
                        'card': new_card,
                        'from_region': region_id,
                        'to_region': 6,
                        'description': f"{card_label}({card_id}) 从区域{region_id}流转到区域6"
                    }
                    analysis['cross_region_flows'].append(flow_info)
                    print(f"    🔄 找到来源: {flow_info['description']}")
                    return
        
        print(f"    🆕 真正的新卡牌: {card_label}({card_id})")

def main():
    debugger = Frame274InheritanceDebugger()
    
    print("🔍 数字孪生ID功能区域6继承问题调试")
    print("=" * 60)
    
    # 分析问题帧序列
    print("\n📋 问题案例分析: frame_00273 → frame_00274")
    problem_sequence = ["frame_00273", "frame_00274"]
    problem_results = debugger.analyze_frame_sequence(problem_sequence)
    
    # 分析正常帧序列作为对比
    print("\n📋 正常案例分析1: frame_00361 → frame_00362")
    normal_sequence1 = ["frame_00361", "frame_00362"]
    normal_results1 = debugger.analyze_frame_sequence(normal_sequence1)
    
    print("\n📋 正常案例分析2: frame_00345 → frame_00346")
    normal_sequence2 = ["frame_00345", "frame_00346"]
    normal_results2 = debugger.analyze_frame_sequence(normal_sequence2)
    
    # 生成详细报告
    print("\n" + "=" * 60)
    print("📊 详细分析报告")
    print("=" * 60)
    
    # 保存调试结果
    debug_output = {
        'problem_case': problem_results,
        'normal_case_1': normal_results1,
        'normal_case_2': normal_results2,
        'analysis_summary': {
            'issue_description': "frame_00274区域6中2九未正确继承，变成了1九",
            'expected_behavior': "区域6从下到上应为：2九 1八 1十",
            'actual_behavior': "区域6从下到上实际为：1八 1十 1九"
        }
    }
    
    # 保存到文件
    with open('debug_frame_274_results.json', 'w', encoding='utf-8') as f:
        json.dump(debug_output, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 调试结果已保存到: debug_frame_274_results.json")

if __name__ == "__main__":
    main()
