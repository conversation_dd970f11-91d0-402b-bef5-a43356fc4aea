"""
第21张牌跟踪器 (Card21Tracker)
单一职责：跟踪对战方庄家的第21张牌

核心功能：
1. 记录对战方庄家第21张牌的消失事件
2. 检测该牌在打牌区域的重现
3. 为AI推理提供已知信息，减少不确定性
4. 符合GAME_RULES_OPTIMIZED.md的第21张牌简单跟踪要求
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class Card21TrackingResult:
    """第21张牌跟踪结果"""
    tracked_cards: Dict[str, Any]  # 当前跟踪的第21张牌信息
    reappeared_cards: List[Dict[str, Any]]  # 重新出现的卡牌
    statistics: Dict[str, Any]

class Card21Tracker:
    """第21张牌跟踪器 - 实现GAME_RULES_OPTIMIZED.md的简单跟踪机制"""
    
    def __init__(self):
        # 第21张牌跟踪记录：{标签: 跟踪信息}
        self.tracked_cards: Dict[str, Dict[str, Any]] = {}
        
        # 跟踪统计
        self.tracking_stats = {
            "total_frames": 0,
            "cards_disappeared": 0,
            "cards_reappeared": 0,
            "currently_tracking": 0,
            "ai_inference_support": 0  # 为AI推理提供支持的次数
        }
        
        # 中央区域定义（对战方抓牌区）
        self.central_regions = [7]  # 对战方抓牌区
        
        # 打牌区域定义（对战方打牌区）
        self.discard_regions = [8]  # 对战方打牌区
        
        logger.info("第21张牌跟踪器初始化完成")
    
    def process_card21_tracking(self, current_cards: List[Dict[str, Any]], 
                               previous_cards: List[Dict[str, Any]] = None,
                               game_context: Dict[str, Any] = None) -> Card21TrackingResult:
        """
        处理第21张牌跟踪
        
        Args:
            current_cards: 当前帧卡牌列表
            previous_cards: 前一帧卡牌列表
            game_context: 游戏上下文信息
            
        Returns:
            跟踪结果
        """
        self.tracking_stats["total_frames"] += 1
        
        # 检测新的消失事件
        if previous_cards:
            self._detect_disappearance_events(current_cards, previous_cards)
        
        # 检测重现事件
        reappeared_cards = self._detect_reappearance_events(current_cards)
        
        # 更新跟踪状态
        self._update_tracking_status()
        
        # 生成AI推理支持信息
        ai_support_info = self._generate_ai_support_info()
        
        logger.info(f"第21张牌跟踪: 当前跟踪{len(self.tracked_cards)}张, "
                   f"重现{len(reappeared_cards)}张")
        
        return Card21TrackingResult(
            tracked_cards=self.tracked_cards.copy(),
            reappeared_cards=reappeared_cards,
            statistics=self._generate_statistics()
        )
    
    def _detect_disappearance_events(self, current_cards: List[Dict[str, Any]], 
                                   previous_cards: List[Dict[str, Any]]):
        """检测中央区域卡牌消失事件"""
        # 获取前一帧中央区域的卡牌
        prev_central_cards = [
            card for card in previous_cards 
            if card.get('group_id') in self.central_regions
        ]
        
        # 获取当前帧中央区域的卡牌
        current_central_cards = [
            card for card in current_cards 
            if card.get('group_id') in self.central_regions
        ]
        
        # 构建当前帧标签集合
        current_labels = {card.get('label') for card in current_central_cards}
        
        # 检测消失的卡牌
        for prev_card in prev_central_cards:
            label = prev_card.get('label')
            if label and label not in current_labels:
                # 记录消失事件
                self._record_disappearance(label, prev_card)
    
    def _record_disappearance(self, label: str, card_info: Dict[str, Any]):
        """记录卡牌消失事件"""
        if label not in self.tracked_cards:
            tracking_info = {
                'label': label,
                'disappeared_frame': self.tracking_stats["total_frames"],
                'original_card_info': card_info.copy(),
                'status': 'tracking',
                'ai_inference_value': True  # 对AI推理有价值
            }
            
            self.tracked_cards[label] = tracking_info
            self.tracking_stats["cards_disappeared"] += 1
            
            logger.info(f"记录第21张牌消失: {label} (帧{self.tracking_stats['total_frames']})")
    
    def _detect_reappearance_events(self, current_cards: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """检测打牌区域重现事件"""
        reappeared_cards = []
        
        # 获取当前帧打牌区域的卡牌
        discard_cards = [
            card for card in current_cards 
            if card.get('group_id') in self.discard_regions
        ]
        
        # 检查是否有跟踪的卡牌重现
        for card in discard_cards:
            label = card.get('label')
            if label in self.tracked_cards:
                # 卡牌重现，停止跟踪
                tracking_info = self.tracked_cards[label]
                tracking_info['status'] = 'reappeared'
                tracking_info['reappeared_frame'] = self.tracking_stats["total_frames"]
                tracking_info['reappeared_card_info'] = card.copy()
                
                reappeared_cards.append({
                    'label': label,
                    'tracking_info': tracking_info,
                    'current_card': card
                })
                
                # 从跟踪列表中移除
                del self.tracked_cards[label]
                self.tracking_stats["cards_reappeared"] += 1
                
                logger.info(f"第21张牌重现: {label} (帧{self.tracking_stats['total_frames']})")
        
        return reappeared_cards
    
    def _update_tracking_status(self):
        """更新跟踪状态"""
        self.tracking_stats["currently_tracking"] = len(self.tracked_cards)
    
    def _generate_ai_support_info(self) -> Dict[str, Any]:
        """生成AI推理支持信息"""
        support_info = {
            'known_opponent_cards': list(self.tracked_cards.keys()),
            'uncertainty_reduction': len(self.tracked_cards),  # 减少的不确定性
            'total_unknown_cards': 80 - len(self.tracked_cards),  # 剩余未知卡牌数
            'inference_confidence_boost': min(len(self.tracked_cards) * 0.05, 0.2)  # 推理置信度提升
        }
        
        if self.tracked_cards:
            self.tracking_stats["ai_inference_support"] += 1
        
        return support_info
    
    def _generate_statistics(self) -> Dict[str, Any]:
        """生成统计信息"""
        return {
            "total_frames": self.tracking_stats["total_frames"],
            "cards_disappeared": self.tracking_stats["cards_disappeared"],
            "cards_reappeared": self.tracking_stats["cards_reappeared"],
            "currently_tracking": self.tracking_stats["currently_tracking"],
            "ai_inference_support": self.tracking_stats["ai_inference_support"],
            "tracking_efficiency": self._calculate_tracking_efficiency()
        }
    
    def _calculate_tracking_efficiency(self) -> float:
        """计算跟踪效率"""
        if self.tracking_stats["cards_disappeared"] == 0:
            return 0.0
        return self.tracking_stats["cards_reappeared"] / self.tracking_stats["cards_disappeared"]
    
    def get_ai_inference_support(self) -> Dict[str, Any]:
        """获取AI推理支持信息"""
        return self._generate_ai_support_info()
    
    def reset_tracking(self):
        """重置跟踪状态"""
        self.tracked_cards.clear()
        self.tracking_stats = {
            "total_frames": 0,
            "cards_disappeared": 0,
            "cards_reappeared": 0,
            "currently_tracking": 0,
            "ai_inference_support": 0
        }
        logger.info("第21张牌跟踪器状态已重置")

def create_card21_tracker():
    """创建第21张牌跟踪器"""
    return Card21Tracker()
