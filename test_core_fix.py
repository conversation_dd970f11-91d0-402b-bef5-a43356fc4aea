#!/usr/bin/env python3
"""
测试暗牌到明牌转换修复的核心逻辑
直接测试继承和ID分配模块
"""

import json
import logging
import sys
from pathlib import Path

# 添加src路径
sys.path.append('src')

from modules.basic_id_assigner import BasicIDAssigner, GlobalIDManager
from modules.simple_inheritor import SimpleInheritor

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CoreFixTester:
    """核心修复测试器"""
    
    def __init__(self):
        # 初始化核心模块
        self.global_id_manager = GlobalIDManager()
        self.basic_id_assigner = BasicIDAssigner(self.global_id_manager)
        self.simple_inheritor = SimpleInheritor()
        
    def create_test_cards(self):
        """创建测试用的卡牌数据"""
        # 模拟frame_00306的区域16卡牌（前一帧）
        prev_cards = [
            {
                'label': '1三暗',
                'twin_id': '1三暗',
                'group_id': 16,
                'bbox': [100, 84.3, 120, 94.3],
                'points': [[100, 84.3], [120, 84.3], [120, 94.3], [100, 94.3]],
                'attributes': {'digital_twin_id': '1三暗'}
            },
            {
                'label': '2三暗',
                'twin_id': '2三暗',
                'group_id': 16,
                'bbox': [100, 65.5, 120, 75.5],
                'points': [[100, 65.5], [120, 65.5], [120, 75.5], [100, 75.5]],
                'attributes': {'digital_twin_id': '2三暗'}
            },
            {
                'label': '3三',
                'twin_id': '3三',
                'group_id': 16,
                'bbox': [100, 47.6, 120, 57.6],
                'points': [[100, 47.6], [120, 47.6], [120, 57.6], [100, 57.6]],
                'attributes': {'digital_twin_id': '3三'}
            }
        ]
        
        # 模拟frame_00307的区域16卡牌（当前帧）
        curr_cards = [
            {
                'label': '三',  # 原来的1三暗转为明牌
                'group_id': 16,
                'bbox': [100, 92.9, 120, 102.9],
                'points': [[100, 92.9], [120, 92.9], [120, 102.9], [100, 102.9]],
                'confidence': 0.95
            },
            {
                'label': '三',  # 原来的2三暗转为明牌
                'group_id': 16,
                'bbox': [100, 73.4, 120, 83.4],
                'points': [[100, 73.4], [120, 73.4], [120, 83.4], [100, 83.4]],
                'confidence': 0.95
            },
            {
                'label': '三',  # 原来的3三保持不变
                'group_id': 16,
                'bbox': [100, 53.5, 120, 63.5],
                'points': [[100, 53.5], [120, 53.5], [120, 63.5], [100, 63.5]],
                'confidence': 0.95
            },
            {
                'label': '三',  # 新出现的4三
                'group_id': 16,
                'bbox': [100, 35.6, 120, 45.6],
                'points': [[100, 35.6], [120, 35.6], [120, 45.6], [100, 45.6]],
                'confidence': 0.95
            }
        ]
        
        return prev_cards, curr_cards
    
    def test_inheritance_logic(self):
        """测试继承逻辑"""
        logger.info("🧪 测试继承逻辑")
        
        prev_cards, curr_cards = self.create_test_cards()
        
        # 设置前一帧数据
        self.simple_inheritor._update_previous_frame(prev_cards)
        
        # 处理继承
        inheritance_result = self.simple_inheritor.process_inheritance(curr_cards)
        
        logger.info(f"继承成功: {len(inheritance_result.inherited_cards)}张")
        logger.info(f"新卡牌: {len(inheritance_result.new_cards)}张")
        
        # 分析继承结果
        logger.info("\n📋 继承结果详情:")
        for card in inheritance_result.inherited_cards:
            dark_to_bright = card.get('dark_to_bright_inherited', False)
            original_dark_id = card.get('attributes', {}).get('original_dark_id', '')
            status = "🌟暗→明" if dark_to_bright else "🔄普通继承"
            logger.info(f"  {card.get('twin_id', 'unknown')} {status} (原ID: {original_dark_id})")
        
        logger.info("\n📋 新卡牌详情:")
        for card in inheritance_result.new_cards:
            rejection_reason = card.get('rejection_reason', '')
            logger.info(f"  {card.get('label', 'unknown')} (拒绝原因: {rejection_reason})")
        
        return inheritance_result
    
    def test_id_assignment_logic(self, inheritance_result):
        """测试ID分配逻辑"""
        logger.info("\n🧪 测试ID分配逻辑")
        
        if not inheritance_result.new_cards:
            logger.info("没有新卡牌需要分配ID")
            return inheritance_result.inherited_cards
        
        # 分配ID，传递已继承的卡牌以避免ID冲突
        id_assignment_result = self.basic_id_assigner.assign_ids(inheritance_result.new_cards, inheritance_result.inherited_cards)
        
        logger.info(f"ID分配完成: {len(id_assignment_result.assigned_cards)}张")
        
        # 分析分配结果
        logger.info("\n📋 ID分配结果详情:")
        for card in id_assignment_result.assigned_cards:
            spatial_consistent = card.get('attributes', {}).get('spatial_consistent_assignment', False)
            spatial_position = card.get('attributes', {}).get('spatial_position', 0)
            status = "🔧空间一致性分配" if spatial_consistent else "🆔常规分配"
            logger.info(f"  {card.get('twin_id', 'unknown')} {status} (位置: {spatial_position})")
        
        # 合并所有卡牌
        all_cards = inheritance_result.inherited_cards + id_assignment_result.assigned_cards
        return all_cards
    
    def analyze_final_results(self, final_cards):
        """分析最终结果"""
        logger.info("\n📊 最终结果分析")
        
        # 提取区域16的卡牌
        region_16_cards = [card for card in final_cards if card.get('group_id') == 16]
        
        # 按Y坐标从下到上排序
        region_16_cards.sort(key=lambda x: self._get_card_y_position(x), reverse=True)
        
        # 提取"三"类型的卡牌
        san_cards = [card for card in region_16_cards if '三' in card.get('twin_id', '')]
        
        logger.info(f"\n🎯 区域16 '三' 类型卡牌序列 (从下到上):")
        for i, card in enumerate(san_cards, 1):
            twin_id = card.get('twin_id', 'unknown')
            y_pos = self._get_card_y_position(card)
            dark_to_bright = card.get('dark_to_bright_inherited', False)
            spatial_consistent = card.get('attributes', {}).get('spatial_consistent_assignment', False)
            
            status_icons = []
            if dark_to_bright:
                status_icons.append("🌟暗→明")
            if spatial_consistent:
                status_icons.append("🔧空间一致性")
            
            status_str = " ".join(status_icons) if status_icons else ""
            logger.info(f"  {i}. {twin_id} (Y: {y_pos:.1f}) {status_str}")
        
        # 检查序列正确性
        expected_sequence = ['1三', '2三', '3三', '4三']
        actual_sequence = [card.get('twin_id', '') for card in san_cards]
        
        logger.info(f"\n🔍 序列检查:")
        logger.info(f"期望序列: {expected_sequence}")
        logger.info(f"实际序列: {actual_sequence}")
        
        if actual_sequence == expected_sequence:
            logger.info("✅ 序列正确！修复成功！")
            return True
        else:
            logger.info("❌ 序列不正确，修复失败")
            return False
    
    def _get_card_y_position(self, card):
        """获取卡牌Y坐标"""
        if 'bbox' in card and len(card['bbox']) >= 2:
            return card['bbox'][1]
        elif 'points' in card and card['points']:
            return min(point[1] for point in card['points'])
        return 0.0
    
    def run_test(self):
        """运行测试"""
        logger.info("🚀 开始暗牌到明牌转换核心修复测试")
        logger.info("=" * 60)
        
        # 测试继承逻辑
        inheritance_result = self.test_inheritance_logic()
        
        # 测试ID分配逻辑
        final_cards = self.test_id_assignment_logic(inheritance_result)
        
        # 分析最终结果
        success = self.analyze_final_results(final_cards)
        
        logger.info("\n" + "=" * 60)
        if success:
            logger.info("🎉 测试成功！暗牌到明牌转换修复生效！")
        else:
            logger.info("💥 测试失败！需要进一步调试修复逻辑")
        
        return success

if __name__ == "__main__":
    tester = CoreFixTester()
    tester.run_test()
