#!/usr/bin/env python3
"""
测试暗牌到明牌转换逻辑修复效果
验证frame_00306→frame_00307的转换是否输出正确的"1三、2三、3三、4三"序列
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DarkToBrightConversionTester:
    """暗牌到明牌转换测试器"""
    
    def __init__(self):
        self.test_results = {}
        
    def load_frame_data(self, frame_path: str) -> Dict:
        """加载帧数据"""
        try:
            with open(frame_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载帧数据失败: {frame_path} - {e}")
            return {}
    
    def extract_region_cards(self, frame_data: Dict, region_id: int) -> List[Dict]:
        """提取指定区域的卡牌数据，按Y坐标从下到上排序"""
        if not frame_data or 'shapes' not in frame_data:
            return []
            
        region_cards = []
        for shape in frame_data['shapes']:
            if shape.get('group_id') == region_id:
                # 计算中心Y坐标
                points = shape.get('points', [])
                if points and len(points) >= 4:
                    y_coords = [p[1] for p in points]
                    center_y = sum(y_coords) / len(y_coords)
                    
                    card_info = {
                        'label': shape.get('label', ''),
                        'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                        'center_y': center_y,
                        'shape': shape
                    }
                    region_cards.append(card_info)
        
        # 按Y坐标排序（从下到上，Y坐标从大到小）
        region_cards.sort(key=lambda x: -x['center_y'])
        return region_cards
    
    def test_frame_conversion(self, prev_frame_path: str, curr_frame_path: str, region_id: int) -> Dict:
        """测试帧间转换"""
        logger.info(f"🧪 测试区域{region_id}的帧间转换: {Path(prev_frame_path).name} → {Path(curr_frame_path).name}")
        
        # 加载帧数据
        prev_data = self.load_frame_data(prev_frame_path)
        curr_data = self.load_frame_data(curr_frame_path)
        
        if not prev_data or not curr_data:
            return {"status": "failed", "error": "无法加载帧数据"}
        
        # 提取区域卡牌
        prev_cards = self.extract_region_cards(prev_data, region_id)
        curr_cards = self.extract_region_cards(curr_data, region_id)
        
        # 分析转换结果
        result = self.analyze_conversion(prev_cards, curr_cards, region_id)
        
        return result
    
    def analyze_conversion(self, prev_cards: List[Dict], curr_cards: List[Dict], region_id: int) -> Dict:
        """分析转换结果"""
        result = {
            "region_id": region_id,
            "prev_frame_cards": [],
            "curr_frame_cards": [],
            "dark_to_bright_conversions": [],
            "issues": [],
            "status": "unknown"
        }
        
        # 记录前一帧卡牌
        for card in prev_cards:
            result["prev_frame_cards"].append({
                "id": card['digital_twin_id'],
                "label": card['label'],
                "y_pos": card['center_y']
            })
        
        # 记录当前帧卡牌
        for card in curr_cards:
            result["curr_frame_cards"].append({
                "id": card['digital_twin_id'],
                "label": card['label'],
                "y_pos": card['center_y']
            })
        
        # 检测暗→明转换
        for prev_card in prev_cards:
            prev_id = prev_card['digital_twin_id']
            if '暗' in prev_id:
                # 查找对应的明牌
                expected_bright_id = prev_id.replace('暗', '')
                found_bright = False
                
                for curr_card in curr_cards:
                    curr_id = curr_card['digital_twin_id']
                    if curr_id == expected_bright_id:
                        result["dark_to_bright_conversions"].append({
                            "dark_id": prev_id,
                            "bright_id": curr_id,
                            "status": "success"
                        })
                        found_bright = True
                        break
                
                if not found_bright:
                    result["dark_to_bright_conversions"].append({
                        "dark_id": prev_id,
                        "bright_id": "missing",
                        "status": "failed"
                    })
                    result["issues"].append(f"暗牌{prev_id}未找到对应明牌{expected_bright_id}")
        
        # 检查ID序列的正确性
        self.check_id_sequence(result)
        
        # 评估整体状态
        if not result["issues"]:
            result["status"] = "success"
        else:
            result["status"] = "failed"
        
        return result
    
    def check_id_sequence(self, result: Dict):
        """检查ID序列的正确性"""
        curr_cards = result["curr_frame_cards"]
        
        # 按类型分组
        cards_by_type = {}
        for card in curr_cards:
            card_id = card["id"]
            # 提取类型（如"1三" → "三"）
            card_type = ''.join([c for c in card_id if not c.isdigit()])
            if card_type not in cards_by_type:
                cards_by_type[card_type] = []
            cards_by_type[card_type].append(card)
        
        # 检查每种类型的ID序列
        for card_type, type_cards in cards_by_type.items():
            if len(type_cards) > 1:
                # 按Y坐标排序（从下到上）
                type_cards.sort(key=lambda x: -x["y_pos"])
                
                # 检查ID序列是否为1、2、3、4...
                expected_sequence = []
                actual_sequence = []
                
                for i, card in enumerate(type_cards, 1):
                    expected_id = f"{i}{card_type}"
                    actual_id = card["id"]
                    
                    expected_sequence.append(expected_id)
                    actual_sequence.append(actual_id)
                
                if expected_sequence != actual_sequence:
                    result["issues"].append(
                        f"类型'{card_type}'的ID序列不正确: "
                        f"期望{expected_sequence}, 实际{actual_sequence}"
                    )
    
    def run_test(self):
        """运行测试"""
        logger.info("🚀 开始暗牌到明牌转换逻辑修复效果测试")
        
        # 测试frame_00306→frame_00307的区域16转换
        prev_frame = "output/calibration_gt_final_with_digital_twin/labels/frame_00306.json"
        curr_frame = "output/calibration_gt_final_with_digital_twin/labels/frame_00307.json"
        
        # 测试区域16
        result_16 = self.test_frame_conversion(prev_frame, curr_frame, 16)
        self.test_results["region_16"] = result_16
        
        # 测试区域6（作为对比）
        result_6 = self.test_frame_conversion(prev_frame, curr_frame, 6)
        self.test_results["region_6"] = result_6
        
        # 输出测试结果
        self.print_test_results()
    
    def print_test_results(self):
        """输出测试结果"""
        logger.info("📊 测试结果报告")
        logger.info("=" * 60)
        
        for region_name, result in self.test_results.items():
            logger.info(f"\n🎯 {region_name.upper()} 测试结果:")
            logger.info(f"状态: {'✅ 成功' if result['status'] == 'success' else '❌ 失败'}")
            
            # 显示前一帧卡牌
            logger.info(f"\n前一帧卡牌 (从下到上):")
            for card in result["prev_frame_cards"]:
                logger.info(f"  {card['id']} (Y: {card['y_pos']:.1f})")
            
            # 显示当前帧卡牌
            logger.info(f"\n当前帧卡牌 (从下到上):")
            for card in result["curr_frame_cards"]:
                logger.info(f"  {card['id']} (Y: {card['y_pos']:.1f})")
            
            # 显示暗→明转换
            if result["dark_to_bright_conversions"]:
                logger.info(f"\n暗→明转换:")
                for conversion in result["dark_to_bright_conversions"]:
                    status_icon = "✅" if conversion["status"] == "success" else "❌"
                    logger.info(f"  {status_icon} {conversion['dark_id']} → {conversion['bright_id']}")
            
            # 显示问题
            if result["issues"]:
                logger.info(f"\n❌ 发现的问题:")
                for issue in result["issues"]:
                    logger.info(f"  - {issue}")
            
            logger.info("-" * 40)

if __name__ == "__main__":
    tester = DarkToBrightConversionTester()
    tester.run_test()
