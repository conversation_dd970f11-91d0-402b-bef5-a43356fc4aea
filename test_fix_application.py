#!/usr/bin/env python3
"""
测试暗牌到明牌转换修复的实际应用
运行完整的处理流程来验证修复效果
"""

import json
import logging
import sys
from pathlib import Path

# 添加src路径
sys.path.append('src')

from modules.data_validator import DataValidator
from modules.basic_id_assigner import BasicIDAssigner, GlobalIDManager
from modules.simple_inheritor import SimpleInheritor
from modules.dark_card_processor import DarkCardProcessor
from modules.phase1_integrator import Phase1Integrator

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FixApplicationTester:
    """修复应用测试器"""
    
    def __init__(self):
        # 初始化处理模块
        self.global_id_manager = GlobalIDManager()
        self.data_validator = DataValidator()
        self.basic_id_assigner = BasicIDAssigner(self.global_id_manager)
        self.simple_inheritor = SimpleInheritor()
        self.dark_card_processor = DarkCardProcessor()
        self.phase1_integrator = Phase1Integrator()
        
    def load_frame_data(self, frame_path: str) -> dict:
        """加载帧数据"""
        try:
            with open(frame_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载帧数据失败: {frame_path} - {e}")
            return {}
    
    def convert_to_processing_format(self, frame_data: dict) -> list:
        """将帧数据转换为处理格式"""
        if not frame_data or 'shapes' not in frame_data:
            return []
        
        cards = []
        for shape in frame_data['shapes']:
            card = {
                'label': shape.get('label', ''),
                'group_id': shape.get('group_id', 0),
                'points': shape.get('points', []),
                'bbox': self._points_to_bbox(shape.get('points', [])),
                'score': shape.get('score', 1.0),
                'confidence': shape.get('confidence', 1.0),
                'region_name': shape.get('region_name', ''),
                'owner': shape.get('owner', ''),
                'description': shape.get('description', ''),
                'difficult': shape.get('difficult', False),
                'shape_type': shape.get('shape_type', 'rectangle'),
                'attributes': shape.get('attributes', {}).copy()
            }
            
            # 如果已有twin_id，保留它
            if 'digital_twin_id' in card['attributes']:
                card['twin_id'] = card['attributes']['digital_twin_id']
            
            cards.append(card)
        
        return cards
    
    def _points_to_bbox(self, points):
        """将points转换为bbox格式"""
        if not points or len(points) < 4:
            return [0, 0, 0, 0]
        
        x_coords = [p[0] for p in points]
        y_coords = [p[1] for p in points]
        
        return [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
    
    def process_frame_transition(self, prev_frame_path: str, curr_frame_path: str):
        """处理帧间转换"""
        logger.info(f"🔄 处理帧间转换: {Path(prev_frame_path).name} → {Path(curr_frame_path).name}")
        
        # 加载帧数据
        prev_data = self.load_frame_data(prev_frame_path)
        curr_data = self.load_frame_data(curr_frame_path)
        
        if not prev_data or not curr_data:
            logger.error("无法加载帧数据")
            return None
        
        # 转换为处理格式
        prev_cards = self.convert_to_processing_format(prev_data)
        curr_cards = self.convert_to_processing_format(curr_data)
        
        logger.info(f"前一帧: {len(prev_cards)}张卡牌")
        logger.info(f"当前帧: {len(curr_cards)}张卡牌")
        
        # 设置前一帧数据到继承器
        self.simple_inheritor._update_previous_frame(prev_cards)
        
        # 步骤1: 数据验证
        logger.info("🔍 步骤1: 数据验证")
        validation_result = self.data_validator.validate(curr_cards)
        validated_cards = validation_result.cleaned_data
        
        # 步骤2: 继承处理
        logger.info("🔄 步骤2: 继承处理")
        inheritance_result = self.simple_inheritor.process_inheritance(validated_cards)
        inherited_cards = inheritance_result.inherited_cards
        new_cards = inheritance_result.new_cards
        
        logger.info(f"继承成功: {len(inherited_cards)}张")
        logger.info(f"新卡牌: {len(new_cards)}张")
        
        # 步骤3: ID分配
        logger.info("🆔 步骤3: ID分配")
        if new_cards:
            id_assignment_result = self.basic_id_assigner.assign_ids(new_cards)
            assigned_cards = id_assignment_result.assigned_cards
        else:
            assigned_cards = []
        
        # 步骤4: 暗牌处理
        logger.info("🌑 步骤4: 暗牌处理")
        all_cards = inherited_cards + assigned_cards
        dark_card_result = self.dark_card_processor.process_dark_cards(all_cards)
        processed_cards = dark_card_result.processed_cards
        
        # 步骤5: 第一阶段集成
        logger.info("🔧 步骤5: 第一阶段集成")
        integration_result = self.phase1_integrator.process_frame(processed_cards)
        final_cards = integration_result.processed_cards
        
        logger.info(f"最终处理完成: {len(final_cards)}张卡牌")
        
        return final_cards
    
    def extract_region_results(self, cards: list, region_id: int) -> list:
        """提取指定区域的处理结果"""
        region_cards = [card for card in cards if card.get('group_id') == region_id]
        
        # 按Y坐标从下到上排序
        region_cards.sort(key=lambda x: self._get_card_y_position(x), reverse=True)
        
        results = []
        for card in region_cards:
            results.append({
                'id': card.get('twin_id', card.get('label', 'unknown')),
                'label': card.get('label', ''),
                'y_pos': self._get_card_y_position(card),
                'inherited': card.get('inherited', False),
                'dark_to_bright': card.get('dark_to_bright_inherited', False)
            })
        
        return results
    
    def _get_card_y_position(self, card):
        """获取卡牌Y坐标"""
        if 'bbox' in card and len(card['bbox']) >= 2:
            return card['bbox'][1]
        elif 'points' in card and card['points']:
            return min(point[1] for point in card['points'])
        return 0.0
    
    def run_test(self):
        """运行测试"""
        logger.info("🚀 开始暗牌到明牌转换修复应用测试")
        
        # 处理frame_00306→frame_00307转换
        prev_frame = "output/calibration_gt_final_with_digital_twin/labels/frame_00306.json"
        curr_frame = "output/calibration_gt_final_with_digital_twin/labels/frame_00307.json"
        
        # 运行处理流程
        final_cards = self.process_frame_transition(prev_frame, curr_frame)
        
        if final_cards is None:
            logger.error("处理失败")
            return
        
        # 提取区域16结果
        region_16_results = self.extract_region_results(final_cards, 16)
        
        # 提取区域6结果
        region_6_results = self.extract_region_results(final_cards, 6)
        
        # 输出结果
        self.print_results(region_16_results, region_6_results)
    
    def print_results(self, region_16_results, region_6_results):
        """输出结果"""
        logger.info("📊 处理结果报告")
        logger.info("=" * 60)
        
        logger.info("\n🎯 区域16处理结果 (从下到上):")
        for i, card in enumerate(region_16_results, 1):
            status_icons = []
            if card['inherited']:
                status_icons.append("🔄继承")
            if card['dark_to_bright']:
                status_icons.append("🌟暗→明")
            
            status_str = " ".join(status_icons) if status_icons else ""
            logger.info(f"  {i}. {card['id']} (Y: {card['y_pos']:.1f}) {status_str}")
        
        logger.info("\n🎯 区域6处理结果 (从下到上):")
        for i, card in enumerate(region_6_results, 1):
            status_icons = []
            if card['inherited']:
                status_icons.append("🔄继承")
            if card['dark_to_bright']:
                status_icons.append("🌟暗→明")
            
            status_str = " ".join(status_icons) if status_icons else ""
            logger.info(f"  {i}. {card['id']} (Y: {card['y_pos']:.1f}) {status_str}")
        
        # 检查区域16的"三"类型卡牌序列
        region_16_san_cards = [card for card in region_16_results if '三' in card['id']]
        if region_16_san_cards:
            logger.info(f"\n🔍 区域16 '三' 类型卡牌序列检查:")
            expected_sequence = ['1三', '2三', '3三', '4三']
            actual_sequence = [card['id'] for card in region_16_san_cards]
            
            logger.info(f"期望序列: {expected_sequence}")
            logger.info(f"实际序列: {actual_sequence}")
            
            if actual_sequence == expected_sequence:
                logger.info("✅ 序列正确！修复成功！")
            else:
                logger.info("❌ 序列不正确，需要进一步调试")

if __name__ == "__main__":
    tester = FixApplicationTester()
    tester.run_test()
