#!/usr/bin/env python3
"""
测试区域6继承逻辑的脚本
分析frame_00273和frame_00274中区域6的状态继承问题
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到路径
sys.path.insert(0, '.')

from calibration_gt_final_processor import CalibrationGTFinalProcessor, FinalProcessorConfig

def analyze_frame_data(frame_path: Path) -> Dict[str, Any]:
    """分析单个帧的数据"""
    with open(frame_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 提取区域6的卡牌
    region6_cards = []
    for shape in data.get('shapes', []):
        if shape.get('group_id') == 6:
            region6_cards.append({
                'label': shape.get('label'),
                'points': shape.get('points'),
                'y_position': shape.get('points', [[0, 0]])[0][1] if shape.get('points') else 0
            })
    
    # 按Y坐标排序（从下到上）
    region6_cards.sort(key=lambda x: -x['y_position'])
    
    return {
        'frame_name': frame_path.stem,
        'region6_cards': region6_cards,
        'total_cards': len(region6_cards)
    }

def test_inheritance_logic():
    """测试继承逻辑"""
    print("🔍 分析区域6的输出错误问题")
    print("=" * 60)

    # 分析原始数据
    print("\n📊 原始数据分析:")
    frame_273_path = Path('legacy_assets/ceshi/calibration_gt/labels/frame_00273.json')
    frame_274_path = Path('legacy_assets/ceshi/calibration_gt/labels/frame_00274.json')

    frame_273_data = analyze_frame_data(frame_273_path)
    frame_274_data = analyze_frame_data(frame_274_path)

    print(f"\nframe_00273 区域6卡牌 (从下到上):")
    for i, card in enumerate(frame_273_data['region6_cards']):
        print(f"  {i+1}. {card['label']} (Y: {card['y_position']:.1f})")

    print(f"\nframe_00274 区域6卡牌 (从下到上):")
    for i, card in enumerate(frame_274_data['region6_cards']):
        print(f"  {i+1}. {card['label']} (Y: {card['y_position']:.1f})")

    # 分析修复前的输出数据
    print("\n📊 修复前输出数据分析:")
    output_273_path = Path('output/calibration_gt_final_with_digital_twin/labels/frame_00273.json')
    output_274_path = Path('output/calibration_gt_final_with_digital_twin/labels/frame_00274.json')

    if output_273_path.exists() and output_274_path.exists():
        print(f"\n修复前 frame_00273 区域6卡牌:")
        with open(output_273_path, 'r', encoding='utf-8') as f:
            output_273_json = json.load(f)
        for shape in output_273_json.get('shapes', []):
            if shape.get('group_id') == 6:
                twin_id = shape.get('attributes', {}).get('digital_twin_id', 'None')
                print(f"  标签: {shape.get('label')}, 数字孪生ID: {twin_id}")

        print(f"\n修复前 frame_00274 区域6卡牌:")
        with open(output_274_path, 'r', encoding='utf-8') as f:
            output_274_json = json.load(f)
        for shape in output_274_json.get('shapes', []):
            if shape.get('group_id') == 6:
                twin_id = shape.get('attributes', {}).get('digital_twin_id', 'None')
                print(f"  标签: {shape.get('label')}, 数字孪生ID: {twin_id}")

    # 测试修复后的继承逻辑
    print("\n🧪 测试修复后的继承逻辑:")
    config = FinalProcessorConfig(
        source_dir='legacy_assets/ceshi/calibration_gt',
        output_dir='output/test_region6_inheritance_fixed'
    )

    processor = CalibrationGTFinalProcessor(config)

    print("\n=== 处理frame_00273 (建立基准状态) ===")
    try:
        # 创建输出目录
        output_dir = Path(config.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        (output_dir / "labels").mkdir(exist_ok=True)
        (output_dir / "images").mkdir(exist_ok=True)

        processor._process_single_frame_final(frame_273_path)
        print("✅ frame_00273 处理完成")
    except Exception as e:
        print(f"⚠️ frame_00273 处理出错: {e}")

    print("\n=== 处理frame_00274 (测试继承) ===")
    try:
        processor._process_single_frame_final(frame_274_path)
        print("✅ frame_00274 处理完成")
    except Exception as e:
        print(f"⚠️ frame_00274 处理出错: {e}")

    # 分析修复后的结果
    print("\n📈 修复后继承分析结果:")
    fixed_273_path = Path('output/test_region6_inheritance_fixed/labels/frame_00273.json')
    fixed_274_path = Path('output/test_region6_inheritance_fixed/labels/frame_00274.json')

    if fixed_273_path.exists():
        print(f"\n修复后 frame_00273 区域6卡牌:")
        with open(fixed_273_path, 'r', encoding='utf-8') as f:
            fixed_273_json = json.load(f)
        for shape in fixed_273_json.get('shapes', []):
            if shape.get('group_id') == 6:
                twin_id = shape.get('attributes', {}).get('digital_twin_id', 'None')
                print(f"  标签: {shape.get('label')}, 数字孪生ID: {twin_id}")

    if fixed_274_path.exists():
        print(f"\n修复后 frame_00274 区域6卡牌:")
        with open(fixed_274_path, 'r', encoding='utf-8') as f:
            fixed_274_json = json.load(f)
        for shape in fixed_274_json.get('shapes', []):
            if shape.get('group_id') == 6:
                twin_id = shape.get('attributes', {}).get('digital_twin_id', 'None')
                print(f"  标签: {shape.get('label')}, 数字孪生ID: {twin_id}")

    print("\n📋 预期结果:")
    print("- frame_00273: 区域6应该从下到上依次为 2九 1八 1十")
    print("- frame_00274: 区域6应该从下到上依次为 2九 1八 1十 (继承上一帧)")
    print("- 修复目标: 2九应该正确继承上一帧的状态")

    return {
        'frame_273_original': frame_273_data,
        'frame_274_original': frame_274_data
    }

def analyze_inheritance_priority():
    """分析继承优先级问题"""
    print("\n🔧 分析继承优先级问题:")
    print("根据用户描述，区域6的正确继承优先级应该是:")
    print("1. 6→6 (本区域状态继承)")
    print("2. 3→6 (观战抓牌区→观战吃碰区)")
    print("3. 7→6 (对战抓牌区→观战吃碰区)")
    print("4. 8→6 (对战打牌区→观战吃碰区)")
    print("5. 1→6 (观战手牌区→观战吃碰区)")
    
    # 检查当前的流转逻辑
    from src.modules.region_transitioner import RegionTransitioner
    transitioner = RegionTransitioner()
    
    print(f"\n当前区域流转路径配置:")
    for source_region, target_regions in transitioner.transition_paths.items():
        if 6 in target_regions:
            print(f"  区域{source_region} → 区域6")

def main():
    """主函数"""
    print("🎯 数字孪生ID功能中区域6的输出错误问题分析")
    print("=" * 80)
    
    # 执行测试
    test_results = test_inheritance_logic()
    
    # 分析继承优先级
    analyze_inheritance_priority()
    
    print("\n" + "=" * 80)
    print("✅ 分析完成")

if __name__ == "__main__":
    main()
