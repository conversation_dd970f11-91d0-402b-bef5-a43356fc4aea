#!/usr/bin/env python3
"""
区域6继承问题修复验证脚本
用于验证frame_00274问题的修复方案有效性
"""

import json
import os
from typing import Dict, List, Any, Optional
from pathlib import Path

class Region6InheritanceFixTester:
    def __init__(self):
        self.output_dir = Path("output/calibration_gt_final_with_digital_twin/labels")
        self.test_results = {}
        
    def load_frame_data(self, frame_name: str) -> Optional[Dict]:
        """加载指定帧的JSON数据"""
        file_path = self.output_dir / f"{frame_name}.json"
        if not file_path.exists():
            return None
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 读取文件失败 {file_path}: {e}")
            return None
    
    def extract_region6_cards(self, frame_data: Dict) -> List[Dict]:
        """提取区域6的卡牌数据，按从下到上排序"""
        if not frame_data or 'shapes' not in frame_data:
            return []
            
        region6_cards = []
        for shape in frame_data['shapes']:
            if shape.get('group_id') == 6:
                # 计算中心Y坐标
                points = shape.get('points', [])
                if points and len(points) >= 4:
                    y_coords = [p[1] for p in points]
                    center_y = sum(y_coords) / len(y_coords)
                    
                    card_info = {
                        'label': shape.get('label', ''),
                        'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                        'center_y': center_y,
                        'shape': shape
                    }
                    region6_cards.append(card_info)
        
        # 按Y坐标排序（从下到上，Y坐标从大到小）
        region6_cards.sort(key=lambda x: -x['center_y'])
        return region6_cards
    
    def test_inheritance_scenarios(self) -> Dict:
        """测试各种继承场景"""
        print("🧪 开始区域6继承场景测试")
        print("=" * 60)
        
        test_cases = [
            {
                'name': '问题案例',
                'frames': ['frame_00273', 'frame_00274'],
                'expected_behavior': '2九应该继承，但实际消失了',
                'expected_region6': ['2九', '1八', '1十']
            },
            {
                'name': '正常案例1', 
                'frames': ['frame_00361', 'frame_00362'],
                'expected_behavior': '所有卡牌正常继承，发生位置移动',
                'expected_region6': ['1贰', '1拾', '2柒']
            },
            {
                'name': '正常案例2',
                'frames': ['frame_00345', 'frame_00346'], 
                'expected_behavior': '所有卡牌完美继承，无变化',
                'expected_region6': ['1八', '1捌', '2捌']
            }
        ]
        
        results = {}
        
        for test_case in test_cases:
            print(f"\n📋 测试 {test_case['name']}: {' → '.join(test_case['frames'])}")
            result = self._test_single_scenario(test_case)
            results[test_case['name']] = result
            
        return results
    
    def _test_single_scenario(self, test_case: Dict) -> Dict:
        """测试单个继承场景"""
        frames = test_case['frames']
        prev_frame, curr_frame = frames[0], frames[1]
        
        # 加载数据
        prev_data = self.load_frame_data(prev_frame)
        curr_data = self.load_frame_data(curr_frame)
        
        if not prev_data or not curr_data:
            return {'status': 'failed', 'reason': '数据加载失败'}
        
        # 提取区域6数据
        prev_region6 = self.extract_region6_cards(prev_data)
        curr_region6 = self.extract_region6_cards(curr_data)
        
        # 分析继承情况
        analysis = self._analyze_inheritance_quality(prev_region6, curr_region6, test_case)
        
        # 打印结果
        self._print_scenario_result(prev_frame, curr_frame, prev_region6, curr_region6, analysis)
        
        return analysis
    
    def _analyze_inheritance_quality(self, prev_cards: List[Dict], curr_cards: List[Dict], test_case: Dict) -> Dict:
        """分析继承质量"""
        analysis = {
            'status': 'unknown',
            'inheritance_rate': 0.0,
            'missing_cards': [],
            'new_cards': [],
            'position_changes': [],
            'issues': [],
            'recommendations': []
        }
        
        # 计算继承率
        prev_ids = {card['digital_twin_id'] for card in prev_cards}
        curr_ids = {card['digital_twin_id'] for card in curr_cards}
        
        inherited_ids = prev_ids & curr_ids
        missing_ids = prev_ids - curr_ids
        new_ids = curr_ids - prev_ids
        
        if prev_ids:
            analysis['inheritance_rate'] = len(inherited_ids) / len(prev_ids) * 100
        
        # 记录变化
        analysis['missing_cards'] = [
            {'id': card['digital_twin_id'], 'label': card['label']} 
            for card in prev_cards if card['digital_twin_id'] in missing_ids
        ]
        
        analysis['new_cards'] = [
            {'id': card['digital_twin_id'], 'label': card['label']} 
            for card in curr_cards if card['digital_twin_id'] in new_ids
        ]
        
        # 检查位置变化
        for prev_card in prev_cards:
            for curr_card in curr_cards:
                if prev_card['digital_twin_id'] == curr_card['digital_twin_id']:
                    y_diff = abs(prev_card['center_y'] - curr_card['center_y'])
                    if y_diff > 5:  # 位置变化阈值
                        analysis['position_changes'].append({
                            'id': prev_card['digital_twin_id'],
                            'y_change': y_diff
                        })
        
        # 评估状态
        if analysis['inheritance_rate'] == 100 and not analysis['missing_cards']:
            analysis['status'] = 'perfect'
        elif analysis['inheritance_rate'] >= 80:
            analysis['status'] = 'good'
        elif analysis['inheritance_rate'] >= 60:
            analysis['status'] = 'acceptable'
        else:
            analysis['status'] = 'problematic'
            
        # 生成问题和建议
        if analysis['missing_cards']:
            analysis['issues'].append(f"有{len(analysis['missing_cards'])}张卡牌消失")
            analysis['recommendations'].append("检查消失卡牌的流转路径")
            
        if analysis['new_cards']:
            analysis['issues'].append(f"有{len(analysis['new_cards'])}张新卡牌出现")
            analysis['recommendations'].append("验证新卡牌的来源合理性")
            
        return analysis
    
    def _print_scenario_result(self, prev_frame: str, curr_frame: str, 
                             prev_cards: List[Dict], curr_cards: List[Dict], 
                             analysis: Dict):
        """打印场景测试结果"""
        print(f"  📊 {prev_frame} → {curr_frame}")
        
        # 显示卡牌变化
        prev_labels = [f"{card['label']}({card['digital_twin_id']})" for card in prev_cards]
        curr_labels = [f"{card['label']}({card['digital_twin_id']})" for card in curr_cards]
        
        print(f"    前一帧: {prev_labels}")
        print(f"    当前帧: {curr_labels}")
        print(f"    继承率: {analysis['inheritance_rate']:.1f}%")
        print(f"    状态: {self._get_status_emoji(analysis['status'])} {analysis['status']}")
        
        if analysis['missing_cards']:
            missing_str = [f"{card['label']}({card['id']})" for card in analysis['missing_cards']]
            print(f"    ❌ 消失卡牌: {missing_str}")
            
        if analysis['new_cards']:
            new_str = [f"{card['label']}({card['id']})" for card in analysis['new_cards']]
            print(f"    🆕 新增卡牌: {new_str}")
            
        if analysis['position_changes']:
            print(f"    🔄 位置变化: {len(analysis['position_changes'])}张卡牌")
            
        if analysis['issues']:
            print(f"    ⚠️  问题: {'; '.join(analysis['issues'])}")
            
    def _get_status_emoji(self, status: str) -> str:
        """获取状态对应的emoji"""
        emoji_map = {
            'perfect': '✅',
            'good': '👍', 
            'acceptable': '⚠️',
            'problematic': '❌',
            'unknown': '❓'
        }
        return emoji_map.get(status, '❓')
    
    def generate_fix_recommendations(self, test_results: Dict) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        # 分析所有测试结果
        problematic_cases = [
            name for name, result in test_results.items() 
            if result.get('status') == 'problematic'
        ]
        
        if problematic_cases:
            recommendations.append("🔧 需要修复的场景:")
            for case in problematic_cases:
                recommendations.append(f"  - {case}")
                
        recommendations.extend([
            "",
            "💡 建议的修复方案:",
            "1. 在SimpleInheritor中添加区域6特殊处理逻辑",
            "2. 增强卡牌消失检测和恢复机制", 
            "3. 添加数据一致性验证",
            "4. 增强继承追踪日志",
            "",
            "🧪 验证步骤:",
            "1. 实施修复方案",
            "2. 重新运行此测试脚本",
            "3. 确保所有场景达到'good'或'perfect'状态"
        ])
        
        return recommendations

def main():
    tester = Region6InheritanceFixTester()
    
    print("🔍 区域6继承问题修复验证")
    print("=" * 60)
    
    # 运行测试
    test_results = tester.test_inheritance_scenarios()
    
    # 生成修复建议
    recommendations = tester.generate_fix_recommendations(test_results)
    
    print("\n" + "=" * 60)
    print("📋 测试总结和修复建议")
    print("=" * 60)
    
    for rec in recommendations:
        print(rec)
    
    # 保存测试结果
    output_file = 'region6_inheritance_test_results.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            'test_results': test_results,
            'recommendations': recommendations,
            'summary': {
                'total_scenarios': len(test_results),
                'problematic_scenarios': len([
                    r for r in test_results.values() 
                    if r.get('status') == 'problematic'
                ])
            }
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 测试结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
